// Import Twilio
const twilio = require('twilio');

// Twilio credentials from Twi<PERSON> Console
const accountSid = '**********************************';
const authToken = 'b9776e63877b801eb76e7a0b8768c0de';
const client = new twilio(accountSid, authToken);

// Twilio phone number and recipient's phone number
const twilioNumber = '+************';
const recipientNumber = '+************';

// Send SMS
client.messages
  .create({
    body: 'Hello! This is a test message from <PERSON>wi<PERSON>.',
    from: twilioNumber,
    to: recipientNumber,
  })
  .then(message => {
    console.log(`Message sent! SID: ${message.sid}`);  // Fixed template literal syntax
  })
  .catch(error => {
    console.error('Error sending message:', error);
  });
