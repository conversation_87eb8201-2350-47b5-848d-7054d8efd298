<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Menstrual Tracker</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      background-color: #f8f4fc;
      margin: 0;
      padding: 0;
    }

    .tracker-container {
      background-color: #ece3ff;
      border-radius: 8px;
      padding: 20px;
      max-width: 400px;
      margin: 30px auto;
      text-align: center;
    }

    .tracker-container h2 {
      margin-bottom: 20px;
    }

    .tracker-inputs {
      display: flex;
      justify-content: space-between;
      margin-bottom: 20px;
    }

    .tracker-input {
      flex: 1;
      margin: 0 10px;
    }

    .tracker-input label {
      display: block;
      margin-bottom: 8px;
      font-weight: bold;
    }

    .tracker-input input {
      width: 100%;
      padding: 8px;
      border: 1px solid #ccc;
      border-radius: 4px;
    }

    .tracker-input button {
      padding: 5px 10px;
      background-color: #ff5fa2;
      color: white;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .tracker-input button:hover {
      background-color: #e14c87;
    }

    .calendar {
      margin: 30px auto;
      max-width: 700px;
    }

    .calendar h3 {
      text-align: center;
      margin-bottom: 10px;
    }

    .calendar table {
      width: 100%;
      border-collapse: collapse;
    }

    .calendar th, .calendar td {
      padding: 10px;
      text-align: center;
      border: 1px solid #ddd;
    }

    .calendar .period-day {
      background-color: #ffcdd2;
      color: #b71c1c;
    }
  </style>
</head>
<body>
  <div class="tracker-container">
    <h2>Menstrual Tracker</h2>
    <div class="tracker-inputs">
      <div class="tracker-input">
        <label for="start-date">Start Date</label>
        <input type="date" id="start-date">
      </div>
      <div class="tracker-input">
        <label for="period-days">Days</label>
        <input type="number" id="period-days" min="1" value="5">
      </div>
      <div class="tracker-input">
        <label for="cycle-days">Cycle Length</label>
        <input type="number" id="cycle-days" min="20" value="28">
      </div>
    </div>
    <button id="track-button">Track Now</button>
  </div>

  <div class="calendar">
    <h3>Upcoming Periods</h3>
    <div id="calendar"></div>
  </div>

  <script>
    function generateCalendar(startDate, periodDays, cycleDays) {
      const calendarDiv = document.getElementById('calendar');
      calendarDiv.innerHTML = '';

      const months = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];
      const start = new Date(startDate);
      const daysToHighlight = [];

      for (let i = 0; i < 90; i += cycleDays) {
        const periodStart = new Date(start.getTime() + i * 24 * 60 * 60 * 1000);
        for (let j = 0; j < periodDays; j++) {
          const periodDay = new Date(periodStart.getTime() + j * 24 * 60 * 60 * 1000);
          daysToHighlight.push(periodDay);
        }
      }

      const currentDate = new Date(startDate);
      currentDate.setDate(1);
      for (let m = 0; m < 3; m++) {
        const monthDiv = document.createElement('div');
        monthDiv.innerHTML = `<h4>${months[currentDate.getMonth()]} ${currentDate.getFullYear()}</h4>`;
        const table = document.createElement('table');
        let header = '<tr>';
        ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"].forEach(day => {
          header += `<th>${day}</th>`;
        });
        header += '</tr>';
        table.innerHTML = header;

        const firstDay = currentDate.getDay();
        const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate();

        let row = '<tr>';
        for (let i = 0; i < firstDay; i++) {
          row += '<td></td>';
        }

        for (let day = 1; day <= daysInMonth; day++) {
          currentDate.setDate(day);
          const isPeriodDay = daysToHighlight.some(date => date.toDateString() === currentDate.toDateString());
          row += `<td class="${isPeriodDay ? 'period-day' : ''}">${day}</td>`;
          if ((day + firstDay) % 7 === 0 || day === daysInMonth) {
            row += '</tr>';
            table.innerHTML += row;
            row = '<tr>';
          }
        }

        monthDiv.appendChild(table);
        calendarDiv.appendChild(monthDiv);
        currentDate.setMonth(currentDate.getMonth() + 1);
        currentDate.setDate(1);
      }
    }

    document.getElementById('track-button').addEventListener('click', () => {
      const startDate = document.getElementById('start-date').value;
      const periodDays = parseInt(document.getElementById('period-days').value);
      const cycleDays = parseInt(document.getElementById('cycle-days').value);

      if (!startDate) {
        alert('Please select a start date.');
        return;
      }

      generateCalendar(startDate, periodDays, cycleDays);
    });
  </script>
</body>
</html>
