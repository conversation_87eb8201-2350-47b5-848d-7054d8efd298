<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Shewin - Health Dashboard</title>
  <style>
    /* General Styles */
body {
  font-family: 'Arial', sans-serif;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  color: #333;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: #ffffff;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.logo {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.logo img {
  height: 40px;
  width: auto;
}

.nav-links {
  display: flex;
  gap: 2rem;
}

.nav-links a {
  text-decoration: none;
  color: #2c3e50;
  font-weight: 500;
  transition: color 0.3s ease;
}

.nav-links a:hover {
  color: #3498db;
}

/* Dashboard Styles */
.dashboard-main {
  padding: 2rem;
}

.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-top: 2rem;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.05);
}

.metric-card h2 {
  margin-top: 0;
  color: #2c3e50;
  font-size: 1.2rem;
  margin-bottom: 1rem;
}

/* Cycle Tracker Styles */
.cycle-progress {
  margin: 1.5rem 0;
}

.progress-bar {
  background-color: #ecf0f1;
  border-radius: 20px;
  height: 12px;
  overflow: hidden;
}

.progress-fill {
  background-color: #3498db;
  height: 100%;
  transition: width 0.5s ease;
}

.date-picker input {
  width: 100%;
  padding: 0.5rem;
  margin-top: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 6px;
}

/* Mood Tracker Styles */
.mood-legends {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  justify-content: center;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-box {
  width: 20px;
  height: 20px;
  border-radius: 4px;
}

.happy { background-color: #4CAF50; }
.stressed { background-color: #f44336; }
.energetic { background-color: #2196F3; }

/* Symptom Tracker Styles */
#symptomList {
  list-style: none;
  padding: 0;
}

#symptomList li {
  padding: 0.8rem;
  margin: 0.5rem 0;
  background-color: #f8f9fa;
  border-radius: 6px;
  display: flex;
  justify-content: space-between;
}

.symptom-severity {
  color: #3498db;
  font-weight: bold;
}

/* Health Tips Styles */
.daily-tip {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 8px;
  margin-top: 1rem;
}

.tip-content {
  font-style: italic;
  margin: 0;
}

.tip-source {
  text-align: right;
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-top: 0.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    padding: 1rem;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .nav-links {
    gap: 1rem;
  }
}

@media (max-width: 480px) {
  .dashboard-main {
    padding: 1rem;
  }
  
  .metric-card {
    padding: 1rem;
  }
}
  </style>
</head>
<body>

<header class="header">
  <div class="logo">
    <img src="C:\Users\<USER>\Downloads\MenoW.jpg" alt="Logo">
    <span>MenoW</span>
  </div>
  <nav class="nav-links">
    <a href="C:\Users\<USER>\Downloads\shwin\shwin\index.html">Home</a>
  </nav>
</header>

<main class="dashboard-main">
  <section class="dashboard">
    <div class="dashboard-container">
      <h1>Your Personal Health Dashboard</h1>
      <div class="dashboard-grid">

        <!-- Cycle Tracker -->
        <div class="metric-card cycle-tracker">
          <h2>Cycle Tracking</h2>
          <div class="cycle-progress">
            <div class="progress-bar">
              <div class="progress-fill" style="width: 0%"></div>
            </div>
          </div>
          <div class="cycle-prediction">
            <p>Next predicted cycle start: <span id="nextCycle">Calculating...</span></p>
          </div>
          <div class="date-picker">
            <label for="selectDate">Select Your Last Period Start:</label>
            <input type="date" id="selectDate">
          </div>
        </div>

        <!-- Mood Tracker -->
        <div class="metric-card mood-tracker">
          <h2><img src="/img/mood-icon.png" alt=""> Mood Analysis</h2>
          <canvas id="moodChart" height="200"></canvas>
          <div class="mood-legends">
            <span class="legend-item"><div class="color-box happy"></div></span>
            <span class="legend-item"><div class="color-box stressed"></div></span>
            <span class="legend-item"><div class="color-box energetic"></div></span>
          </div>
        </div>

        <!-- Symptom Tracker (NEW) -->
        <div class="metric-card symptom-tracker">
          <h2>Symptom Tracker</h2>
          <ul id="symptomList">
            <li> Cramps: <span class="symptom-severity" id="crampSeverity">-</span> </li>
            <li> Headache: <span class="symptom-severity" id="headacheSeverity">-</span> </li>
            <li> Bloating: <span class="symptom-severity" id="bloatingSeverity">-</span> </li>
          </ul>
        </div>

        <!-- Health Tips -->
        <div class="metric-card health-tips">
          <h2>Daily Health Tip</h2>
          <div class="daily-tip">
            <p class="tip-content" id="dailyTip">Loading tip...</p>
            <div class="tip-source">- MenoW Health Assistant</div>
          </div>
        </div>

      </div>
    </div>
  </section>
</main>

<!-- Scripts -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
const ctx = document.getElementById('moodChart').getContext('2d');
let moodChart;

// Assuming your CSV avg cycle length was calculated around 28 days
const averageCycleLength = 28;

// Simulated symptoms based on cycle day
const symptomsByDay = [
  { cramps: 4, headache: 3, bloating: 2 }, // day 1
  { cramps: 3, headache: 2, bloating: 2 }, // day 2
  { cramps: 2, headache: 2, bloating: 3 }, // and so on
  { cramps: 1, headache: 1, bloating: 3 },
  { cramps: 0, headache: 0, bloating: 1 },
];

const tipsByDay = [
  "Use a warm compress to ease cramps.",
  "Stay hydrated to reduce bloating.",
  "Take short naps to combat fatigue.",
  "Eat iron-rich foods like spinach.",
  "Practice gentle yoga stretches."
];

function generateMoodData(selectedDate) {
  const labels = [];
  const stressedData = [];
  const happyData = [];
  const energeticData = [];

  for (let i = 0; i < 7; i++) {
    const date = new Date(selectedDate);
    date.setDate(date.getDate() + i);
    const options = { month: 'short', day: 'numeric' };
    labels.push(date.toLocaleDateString('en-US', options));

    let dayInCycle = i % averageCycleLength;
    let stressLevel = 3;
    let happyLevel = 3;
    let energeticLevel = 3;

    if (dayInCycle < 5) {
      stressLevel = 4;
      happyLevel = 2;
      energeticLevel = 2;
    } else if (dayInCycle >= 14 && dayInCycle <= 20) {
      happyLevel = 5;
      energeticLevel = 5;
      stressLevel = 2;
    }

    stressedData.push(stressLevel);
    happyData.push(happyLevel);
    energeticData.push(energeticLevel);
  }

  return { labels, stressedData, happyData, energeticData };
}

function updateChart(selectedDate) {
  const { labels, stressedData, happyData, energeticData } = generateMoodData(selectedDate);

  const newData = {
    labels: labels,
    datasets: [
      {
        label: 'Happy',
        data: happyData,
        borderColor: '#4CAF50',
        backgroundColor: 'rgba(76, 175, 80, 0.2)',
        fill: true,
        tension: 0.4
      },
      {
        label: 'Stressed',
        data: stressedData,
        borderColor: '#f44336',
        backgroundColor: 'rgba(244, 67, 54, 0.2)',
        fill: true,
        tension: 0.4
      },
      {
        label: 'Energetic',
        data: energeticData,
        borderColor: '#2196F3',
        backgroundColor: 'rgba(33, 150, 243, 0.2)',
        fill: true,
        tension: 0.4
      }
    ]
  };

  if (moodChart) {
    moodChart.data = newData;
    moodChart.update();
  } else {
    moodChart = new Chart(ctx, {
      type: 'line',
      data: newData,
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'bottom'
          },
          title: {
            display: true,
            text: 'Mood Patterns (7 Days)'
          }
        },
        scales: {
          y: {
            min: 0,
            max: 5,
            ticks: { stepSize: 1 }
          }
        }
      }
    });
  }
}

document.getElementById('selectDate').addEventListener('change', function () {
  const selectedDate = new Date(this.value);

  // Update Mood Chart
  updateChart(selectedDate);

  // Calculate next cycle
  const nextCycleDate = new Date(selectedDate);
  nextCycleDate.setDate(selectedDate.getDate() + averageCycleLength);
  const options = { year: 'numeric', month: 'long', day: 'numeric' };
  document.getElementById('nextCycle').textContent = nextCycleDate.toLocaleDateString('en-US', options);

  // Show Symptoms
  const dayOffset = (new Date() - selectedDate) / (1000 * 3600 * 24);
  const todaySymptoms = symptomsByDay[Math.floor(dayOffset) % symptomsByDay.length];
  document.getElementById('crampSeverity').textContent = todaySymptoms.cramps + "/5";
  document.getElementById('headacheSeverity').textContent = todaySymptoms.headache + "/5";
  document.getElementById('bloatingSeverity').textContent = todaySymptoms.bloating + "/5";

  // Health Tip
  const todayTip = tipsByDay[Math.floor(dayOffset) % tipsByDay.length];
  document.getElementById('dailyTip').textContent = todayTip;
});

// Initialize
const today = new Date();
updateChart(today);

</script>
</body>
</html>