body {
    margin: 0;
    font-family: Arial, sans-serif;
}

.header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 20px;
    background-color: white;
    border-bottom: 1px solid #ddd;
}

.logo {
    display: flex;
    align-items: center;
}

.logo img {
    height: 40px;
    margin-right: 10px;
}

.logo span {
    font-size: 24px;
    font-weight: bold;
    color: #000;
}

.nav-links {
    display: flex;
    gap: 20px;
}

.nav-links a {
    text-decoration: none;
    font-size: 16px;
    color: #000;
    transition: color 0.3s;
}

.nav-links a:hover {
    color: #007BFF;
}

.auth-links {
    display: flex;
    gap: 15px;
}

.auth-links a {
    text-decoration: none;
    font-size: 16px;
    color: #000;
    transition: color 0.3s;
}

.auth-links a:hover {
    color: #007BFF;
}
body {
    margin: 0;
    font-family: Arial, sans-serif;
    background-color: #f8f9fa;
    color: #333;
}

.container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 50px;
    max-width: 1200px;
    margin: 0 auto;
}

.text-content {
    max-width: 50%;
}

.text-content h1 {
    font-size: 48px;
    font-weight: bold;
    margin-bottom: 20px;
    color: #111;
}

.text-content p {
    font-size: 18px;
    line-height: 1.6;
    margin-bottom: 30px;
}

.text-content .btn {
    display: inline-block;
    padding: 15px 30px;
    background-color: #ff0077;
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-decoration: none;
    border-radius: 25px;
    transition: background-color 0.3s;
}

.text-content .btn:hover {
    background-color: #e6006a;
}

.image-content {
    max-width: 45%;
    position: relative;
}

.image-content img {
    width: 100%;
    border-radius: 15px;
}

.quote-box {
    position: absolute;
    bottom: 20px;
    left: -400px;
    background-color: #001f3f;
    color: white;
    padding: 20px;
    border-radius: 10px;
    max-width: 300px;
    font-style: italic;
    font-size: 16px;
}

.quote-box::before {
    content: '\201C';
    font-size: 36px;
    color: #ffcc0000;
    margin-right: 5px;
}

.quote-box .author {
    display: block;
    margin-top: 10px;
    text-align: right;
    font-size: 14px;
    color: #ffcc00;
}




.services {
    padding: 60px 20px;
    background: linear-gradient(135deg, #f8f9fa, #e3f2fd);
    text-align: center;
}

.services-header {
    margin-bottom: 40px;
}

.services-header h2 {
    font-size: 40px;
    font-weight: bold;
    color: #001f3f;
    text-transform: uppercase;
    letter-spacing: 2px;
    position: relative;
}

.services-header h2::after {
    content: "";
    display: block;
    width: 80px;
    height: 4px;
    background-color: #ff0077;
    margin: 10px auto 0;
}

.services-header p {
    font-size: 20px;
    color: #555;
    max-width: 800px;
    margin: 10px auto 0;
    line-height: 1.8;
}

.services-container {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 30px;
    margin-top: 40px;
}

.service-item {
    background: #fff;
    border-radius: 15px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 6px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.service-item:hover {
    transform: scale(1.05);
    box-shadow: 0 12px 20px rgba(0, 0, 0, 0.2);
}

.service-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(to bottom right, #ff0077, #ffcc00);
    opacity: 0.1;
    z-index: -1;
    transition: opacity 0.3s;
}

.service-item:hover::before {
    opacity: 0.3;
}

.service-icon img {
    width: 60px;
    margin-bottom: 20px;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.2));
}

.service-item h3 {
    font-size: 22px;
    font-weight: bold;
    color: #001f3f;
    margin-bottom: 15px;
}

.service-item p {
    font-size: 16px;
    color: #666;
    line-height: 1.6;
}

.service-item a {
    display: inline-block;
    margin-top: 15px;
    padding: 10px 20px;
    font-size: 16px;
    font-weight: bold;
    color: white;
    background-color: #ff0077;
    text-decoration: none;
    border-radius: 25px;
    transition: background-color 0.3s ease;
}

.service-item a:hover {
    background-color: #e6006a;
}








/* Contact Section Styles */
.mh-contact-section {
    background: linear-gradient(135deg, #ffdde1, #ee9ca7); /* Gradient background */
    padding: 70px 20px;
    border-top: 5px solid #d63384;
    border-bottom: 5px solid #d63384;
  }
  
  .mh-container {
    max-width: 900px;
    margin: 0 auto;
    text-align: center;
    padding: 20px;
    background: #fff;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
  }
  
  .mh-heading {
    font-size: 2.5rem;
    color: #d63384;
    margin-bottom: 15px;
    font-weight: bold;
  }
  
  .mh-description {
    font-size: 1rem;
    color: #555;
    margin-bottom: 30px;
  }
  
  .mh-contact-form {
    margin-top: 20px;
  }
  
  .mh-form-group {
    margin-bottom: 20px;
    text-align: left;
  }
  
  .mh-label {
    display: block;
    font-size: 1rem;
    color: #333;
    margin-bottom: 5px;
    font-weight: bold;
  }
  
  .mh-input,
  .mh-textarea {
    width: 100%;
    padding: 12px 5px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 1rem;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
  }
  
  .mh-input:focus,
  .mh-textarea:focus {
    border-color: #d63384;
    outline: none;
    box-shadow: 0 0 8px rgba(214, 51, 132, 0.5);
  }
  
  .mh-textarea {
    resize: none;
  }
  
  .mh-submit-btn {
    background-color: #d63384;
    color: #fff;
    padding: 12px 25px;
    font-size: 1.2rem;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
  }
  
  .mh-submit-btn:hover {
    background-color: #c02574;
    transform: scale(1.05);
  }
  
  .mh-submit-btn:active {
    transform: scale(0.98);
  }
  
  @media (max-width: 768px) {
    .mh-contact-section {
      padding: 50px 10px;
    }
  
    .mh-container {
      padding: 15px;
    }
  
    .mh-heading {
      font-size: 2rem;
    }
  
    .mh-submit-btn {
      width: 100%;
    }
  }

.footer {
    background-color: #222;
    color: #eee;
    text-align: center;
    padding: 20px 0;
    font-family: Arial, sans-serif;
  }
  
  .footer-links {
    margin-bottom: 10px;
  }
  
  .footer-links a {
    color: #eee;
    text-decoration: none;
    margin: 0 15px;
    font-weight: bold;
    transition: color 0.3s;
  }
  
  .footer-links a:hover {
    color: #ff9800;
  }
  
  .footer-copy {
    font-size: 14px;
    color: #aaa;
  }
