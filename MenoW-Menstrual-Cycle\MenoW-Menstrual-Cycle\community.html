<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CycleCare - Menstrual Health Community</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        body {
            background-color: #fff0f5;
        }

        .header {
            background-color: #fff;
            padding: 1rem;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            position: sticky;
            top: 0;
            z-index: 100;
        }

        .nav {
            max-width: 1200px;
            margin: 0 auto;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .logo {
            font-size: 1.5rem;
            color: #d63384;
            font-weight: bold;
        }

        .nav-links a {
            margin-left: 2rem;
            text-decoration: none;
            color: #333;
            font-weight: 500;
        }

        .hero {
            background: linear-gradient(45deg, #ff99cc, #ff6699);
            padding: 4rem 2rem;
            text-align: center;
            color: white;
        }

        .features {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }

        .card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
        }

        .forum-section {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }

        .post-form {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
        }

        textarea {
            width: 100%;
            padding: 1rem;
            margin: 1rem 0;
            border: 1px solid #ddd;
            border-radius: 5px;
            resize: vertical;
        }

        .btn {
            background-color: #d63384;
            color: white;
            padding: 0.5rem 1.5rem;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #c22575;
        }

        .posts {
            background: white;
            padding: 2rem;
            border-radius: 10px;
        }

        .post {
            margin-bottom: 1.5rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .expert-chat {
            background: white;
            padding: 2rem;
            margin: 2rem auto;
            max-width: 1200px;
            border-radius: 10px;
        }

        .chat-window {
            height: 400px;
            border: 1px solid #ddd;
            margin: 1rem 0;
            padding: 1rem;
            overflow-y: auto;
        }

        .message {
            margin: 0.5rem 0;
            padding: 0.5rem;
            background: #f8f8f8;
            border-radius: 5px;
        }

        footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 2rem;
            margin-top: 3rem;
        }
    </style>
</head>
<body>
   
    <section class="hero" id="home">
        <h1>Welcome to CycleCare</h1>
        <p>Your supportive community for menstrual health education and support</p>
    </section>

    <section class="features">
      
        <div class="card">
            <h3>Community Forum</h3>
            <p>Connect with others and share experiences</p>
            
        </div>
        <div class="card">
            <h3>Expert Advice</h3>
            <p>Chat with healthcare professionals</p>
        </div>
    </section>

    <section class="forum-section" id="community">
        
        <div class="post-form">
            <h2>Start a Discussion</h2>
            <textarea id="post-content" placeholder="Share your thoughts..."></textarea>
            <button class="btn" onclick="createPost()">Post</button>
        </div>
        <div class="posts" id="posts-container">
            <!-- Posts will be dynamically added here -->
        </div>
    </section>

    <!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CycleCare - Connect with Doctors</title>
    <style>
        /* Add these new styles */
        .doctors-section {
            max-width: 1200px;
            margin: 2rem auto;
            padding: 2rem;
        }

        .doctor-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .doctor-card {
            background: white;
            padding: 1.5rem;
            border-radius: 10px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            text-align: center;
        }

        .doctor-image {
            width: 150px;
            height: 150px;
            border-radius: 50%;
            object-fit: cover;
            margin: 0 auto 1rem;
        }

        .contact-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            background: white;
            max-width: 500px;
            margin: 2rem auto;
            padding: 2rem;
            border-radius: 10px;
            position: relative;
        }

        .close-btn {
            position: absolute;
            top: 1rem;
            right: 1rem;
            cursor: pointer;
            font-size: 1.5rem;
        }

        .contact-form input,
        .contact-form textarea {
            width: 100%;
            padding: 0.5rem;
            margin: 0.5rem 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <!-- Add this new section -->
    <section class="doctors-section" id="experts">
        <h2>Connect with Healthcare Professionals</h2>
        <div class="doctor-grid" id="doctors-list">
            <!-- Doctor cards will be dynamically added here -->
        </div>
    </section>

    <!-- Add this contact modal -->
    <div class="contact-modal" id="contactModal">
        <div class="modal-content">
            <span class="close-btn" onclick="closeModal()">&times;</span>
            <h3 id="doctorName">Contact Dr. Smith</h3>
            <form class="contact-form" onsubmit="sendMessage(event)">
                <input type="text" placeholder="Your Name" id="userName" required>
                <input type="email" placeholder="Your Email" id="userEmail" required>
                <textarea rows="4" placeholder="Your Message" id="userMessage" required></textarea>
                <button type="submit" class="btn">Send Message</button>
            </form>
        </div>
    </div>

    <script>
        // Doctor data
        const doctors = [
            {
                id: 1,
                name: "Dr. Priya Sharma",
                specialty: "Gynecologist",
                experience: "15 years",
                image: "https://via.placeholder.com/150",
                languages: ["English", "Hindi", "Marathi"]
            },
            {
                id: 2,
                name: "Dr. Anjali Mehta",
                specialty: "Nutritionist",
                experience: "10 years",
                image: "https://via.placeholder.com/150",
                languages: ["English", "Gujarati"]
            },
            {
                id: 3,
                name: "Dr. Sneha Patel",
                specialty: "General Physician",
                experience: "8 years",
                image: "https://via.placeholder.com/150",
                languages: ["English", "Hindi"]
            }
        ];

        // Initialize doctors list
        function initDoctors() {
            const doctorsList = document.getElementById('doctors-list');
            doctors.forEach(doctor => {
                const doctorCard = document.createElement('div');
                doctorCard.className = 'doctor-card';
                doctorCard.innerHTML = `
                    <img src="${doctor.image}" alt="${doctor.name}" class="doctor-image">
                    <h3>${doctor.name}</h3>
                    <p><strong>Specialty:</strong> ${doctor.specialty}</p>
                    <p><strong>Experience:</strong> ${doctor.experience}</p>
                    <p><strong>Languages:</strong> ${doctor.languages.join(', ')}</p>
                    <button class="btn" onclick="openContactModal(${doctor.id})">Contact</button>
                `;
                doctorsList.appendChild(doctorCard);
            });
        }

        // Modal functions
        let selectedDoctor = null;

        function openContactModal(doctorId) {
            selectedDoctor = doctors.find(d => d.id === doctorId);
            document.getElementById('doctorName').textContent = `Contact ${selectedDoctor.name}`;
            document.getElementById('contactModal').style.display = 'block';
        }

        function closeModal() {
            document.getElementById('contactModal').style.display = 'none';
            selectedDoctor = null;
        }

        // Form submission
        function sendMessage(event) {
            event.preventDefault();
            const userName = document.getElementById('userName').value;
            const userEmail = document.getElementById('userEmail').value;
            const userMessage = document.getElementById('userMessage').value;

            // Here you would typically send this data to a server
            console.log('Message to:', selectedDoctor.name);
            console.log('From:', userName, userEmail);
            console.log('Message:', userMessage);

            // Show confirmation
            alert(`Your message to ${selectedDoctor.name} has been sent!`);
            closeModal();
            
            // Clear form fields
            document.getElementById('userName').value = '';
            document.getElementById('userEmail').value = '';
            document.getElementById('userMessage').value = '';
        }

        // Initialize when page loads
        window.onload = initDoctors;

        // Close modal when clicking outside
        window.onclick = function(event) {
            const modal = document.getElementById('contactModal');
            if (event.target === modal) {
                closeModal();
            }
        }
    </script>
</body>
</html>

    <footer>
        <p>© 2023 CycleCare. All rights reserved.</p>
    </footer>

    <script>
        // Community Forum Functionality
        function createPost() {
            const postContent = document.getElementById('post-content').value;
            if (postContent.trim()) {
                const postDiv = document.createElement('div');
                postDiv.className = 'post';
                postDiv.innerHTML = `
                    <h4>User123</h4>
                    <p>${postContent}</p>
                    <small>Just now</small>
                `;
                document.getElementById('posts-container').prepend(postDiv);
                document.getElementById('post-content').value = '';
            }
        }

        // Expert Chat Functionality
        function sendMessage() {
            const userInput = document.getElementById('user-input');
            const message = userInput.value.trim();
            
            if (message) {
                // Add user message
                const chatWindow = document.getElementById('chat-window');
                const userMessage = document.createElement('div');
                userMessage.className = 'message user';
                userMessage.textContent = `You: ${message}`;
                chatWindow.appendChild(userMessage);

                // Simulate expert response
                setTimeout(() => {
                    const expertResponse = document.createElement('div');
                    expertResponse.className = 'message expert';
                    expertResponse.textContent = 'Expert: Thank you for your question. Our team will respond shortly.';
                    chatWindow.appendChild(expertResponse);
                    chatWindow.scrollTop = chatWindow.scrollHeight;
                }, 1000);

                userInput.value = '';
                chatWindow.scrollTop = chatWindow.scrollHeight;
            }
        }

        // Smooth scroll for navigation
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                document.querySelector(this.getAttribute('href')).scrollIntoView({
                    behavior: 'smooth'
                });
            });
        });
    </script>
</body>
</html>