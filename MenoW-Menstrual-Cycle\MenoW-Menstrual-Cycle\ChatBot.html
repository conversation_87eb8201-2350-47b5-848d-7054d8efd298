<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Menstrual Health Assistant</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background-color: #ffe6f2;
            margin: 0;
            padding: 20px;
        }

        .chat-container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            border-radius: 15px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }

        .chat-header {
            background-color: #ff69b4;
            color: white;
            padding: 20px;
            border-radius: 15px 15px 0 0;
            text-align: center;
        }

        .chat-body {
            height: 400px;
            overflow-y: auto;
            padding: 20px;
        }

        .message {
            margin: 10px 0;
            padding: 10px 15px;
            border-radius: 20px;
            max-width: 70%;
        }

        .bot-message {
            background-color: #f8e0e6;
            margin-right: auto;
        }

        .user-message {
            background-color: #ffb6c1;
            margin-left: auto;
        }

        .input-container {
            display: flex;
            padding: 20px;
            border-top: 1px solid #eee;
        }

        #user-input {
            flex: 1;
            padding: 10px;
            border: 2px solid #ff69b4;
            border-radius: 25px;
            margin-right: 10px;
        }

        #send-btn {
            background-color: #ff69b4;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 25px;
            cursor: pointer;
            transition: background-color 0.3s;
        }

        #send-btn:hover {
            background-color: #ff1493;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <h2>🌸 Menstrual Health Guide 🌸</h2>
        </div>
        <div class="chat-body" id="chat-body">
            <div class="message bot-message">
                Hi! I can help with:<br>
                - Menstrual cycle phases<br>
                - Recommended foods<br>
                - Foods to avoid<br>
                - Exercise tips<br>
                - Pain management<br>
                Try asking: "What foods reduce cramps?" or "Best exercises during periods?"
            </div>
        </div>
        <div class="input-container">
            <input type="text" id="user-input" placeholder="Type your question...">
            <button id="send-btn">Send</button>
        </div>
    </div>

    <script>
        const knowledgeBase = {
            // Menstrual Cycle Phases
            'cycle|phases|stages': "Menstrual Cycle Phases:\n\n1. Menstrual Phase (Days 1-5)\n2. Follicular Phase (Days 6-14)\n3. Ovulation Phase (Day 14)\n4. Luteal Phase (Days 15-28)\nTrack your symptoms in each phase!",
            
            // Recommended Foods
            'eat|food|diet|nutrition': "Best Foods During Periods:\n\n✅ Iron-rich: Spinach, lentils\n✅ Omega-3s: Salmon, walnuts\n✅ Magnesium: Dark chocolate, bananas\n✅ Vitamin B6: Chickpeas, potatoes\n✅ Hydrating foods: Cucumber, watermelon",
            
            // Foods to Avoid
            'avoid|bad|limit': "Foods to Limit:\n\n❌ High sodium: Processed snacks\n❌ Caffeine: Coffee, energy drinks\n❌ Alcohol: Increases bloating\n❌ Sugary foods: Spikes energy crashes\n❌ Fatty foods: Increases cramps",
            
            // Exercises
            'exercise|workout|yoga|activity': "Recommended Exercises:\n\n🧘 Yoga: Child's pose, cat-cow\n🚶♀ Walking: Low-impact cardio\n🏊♀ Swimming: Water resistance\n🤸♀ Pilates: Core strengthening\n💃 Dancing: Mood-boosting movement",
            
            // Pain Relief
            'pain|cramps|ache': "Pain Management:\n\n🔥 Heat therapy: Heating pad\n💊 Medication: Ibuprofen\n🌿 Herbal tea: Ginger or chamomile\n💆♀ Massage: Lower abdomen\n🛀 Warm bath: Epsom salts",
            
            // General Tips
            'tips|advice|general': "General Wellness Tips:\n\n💧 Stay hydrated\n😴 Get 7-8 hours sleep\n📱 Use cycle tracking apps\n🩸 Change products regularly\n🧘♀ Practice stress management"
        };

        const chatBody = document.getElementById('chat-body');
        const userInput = document.getElementById('user-input');
        const sendBtn = document.getElementById('send-btn');

        function addMessage(text, isUser) {
            const messageDiv = document.createElement('div');
            messageDiv.classList.add('message');
            messageDiv.classList.add(isUser ? 'user-message' : 'bot-message');
            messageDiv.innerHTML = text.replace(/\n/g, '<br>');
            chatBody.appendChild(messageDiv);
            chatBody.scrollTop = chatBody.scrollHeight;
        }

        function getResponse(userMessage) {
            const lowerMessage = userMessage.toLowerCase();
            
            for(const keywords in knowledgeBase) {
                if(keywords.split('|').some(kw => lowerMessage.includes(kw))) {
                    return knowledgeBase[keywords];
                }
            }
            
            return "I'm specialized in menstrual health! Try asking about:\n- Cycle phases\n- Nutrition advice\n- Exercise tips\n- Pain management\n- General wellness";
        }

        sendBtn.addEventListener('click', () => {
            const message = userInput.value.trim();
            if(message) {
                addMessage(message, true);
                userInput.value = '';
                
                setTimeout(() => {
                    const response = getResponse(message);
                    addMessage(response, false);
                }, 500);
            }
        });

        userInput.addEventListener('keypress', (e) => {
            if(e.key === 'Enter') {
                sendBtn.click();
            }
        });
    </script>
</body>
</html>