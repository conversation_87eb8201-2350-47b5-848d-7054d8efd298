
  
  <style>
  /* Updated Symptom Card Styling */
  .enhanced-symptom-card {
    background: linear-gradient(145deg, #f6f8fc, #e0e7ff);
    border-radius: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    padding: 30px;
    margin: 20px;
    font-family: 'Poppins', sans-serif;
  }
  
  .enhanced-symptom-card h2 {
    text-align: center;
    margin-bottom: 20px;
    color: #4a4a8c;
  }
  
  .date-input {
    width: 100%;
    padding: 12px;
    border-radius: 10px;
    border: 1px solid #b0b0d0;
    margin-top: 10px;
    margin-bottom: 20px;
    font-size: 16px;
  }
  
  .analyze-btn {
    width: 100%;
    padding: 14px;
    background: #6c63ff;
    color: white;
    font-size: 16px;
    border: none;
    border-radius: 10px;
    cursor: pointer;
    margin-bottom: 20px;
    transition: background 0.3s;
  }
  
  .analyze-btn:hover {
    background: #5548d9;
  }
  
  .phase-badge {
    text-align: center;
    font-size: 18px;
    font-weight: bold;
    padding: 10px;
    border-radius: 12px;
    margin-bottom: 20px;
  }
  
  .symptom-result, .health-tip {
    background: white;
    padding: 20px;
    border-radius: 12px;
    margin-bottom: 10px;
    box-shadow: 0 2px 10px rgba(100, 100, 200, 0.1);
    font-size: 16px;
  }
  </style>
  
  <!-- Symptom Checker Card with Styles -->
<div class="metric-card symptom-checker enhanced-symptom-card">
    <h2>🩺 Personalized Cycle Insights</h2>
  
    <label for="cycleDate">📅 Select Your Cycle Start Date:</label>
    <input type="date" id="cycleDate" class="date-input">
  
    <button type="button" class="analyze-btn" onclick="analyzeCycle()">Analyze My Cycle</button>
  
    <div id="phaseBadge" class="phase-badge"></div>
    <div id="symptomResult" class="symptom-result"></div>
    <div id="healthTip" class="health-tip"></div>
</div>
<script>
function analyzeCycle() {
    const selectedDate = new Date(document.getElementById('cycleDate').value);
    const today = new Date();
    const cycleLength = 28;
    const diffTime = today - selectedDate;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    const dayInCycle = diffDays % cycleLength;

    let phase = "";
    let symptoms = "";
    let tip = "";
    let phaseColor = "";

    if (dayInCycle <= 5) {
        phase = "🩸 Menstrual Phase";
        symptoms = "Common: Cramps, low energy, back pain, mood swings. \n Less Common: Nausea, headaches.";
        tip = "Stay hydrated, eat iron-rich foods, and use a heating pad for cramps. Light walking may help ease discomfort.";
        phaseColor = "#ff7b7b";
    } else if (dayInCycle <= 13) {
        phase = "🌱 Follicular Phase";
        symptoms = "Common: Boosted energy, better skin, improved focus.\n Less Common: Mild breast tenderness.";
        tip = "Great time for intense workouts, new projects, and planning. Include lean proteins and leafy greens.";
        phaseColor = "#66cc99";
    } else if (dayInCycle === 14) {
        phase = "🔥 Ovulation Phase";
        symptoms = "Common: Peak fertility, cervical mucus changes, heightened senses.\n Less Common: Ovulation pain (Mittelschmerz).";
        tip = "Stay active, hydrate well. It's a fertile window if planning for pregnancy!";
        phaseColor = "#ffb347";
    } else if (dayInCycle <= 21) {
        phase = "🌙 Luteal Phase (Early)";
        symptoms = "Common: Mood dips, bloating, cravings, fatigue.\n Less Common: Acne flare-ups.";
        tip = "Eat foods rich in magnesium (bananas, dark chocolate). Practice calming activities like yoga.";
        phaseColor = "#9370db";
    } else {
        phase = "🌙 Luteal Phase (Late)";
        symptoms = "Common: PMS (emotional sensitivity, irritability), cramps, lower back pain.\n Less Common: Insomnia.";
        tip = "Reduce salty foods, stay hydrated, and consider gentle stretching or meditation.";
        phaseColor = "#8a2be2";
    }

    // Corrected template literals with backticks
    document.getElementById('phaseBadge').innerHTML =` ${phase}`;
    document.getElementById('phaseBadge').style.backgroundColor = phaseColor;
    document.getElementById('phaseBadge').style.color = "white";

    document.getElementById('symptomResult').innerHTML = `<strong>Symptoms:</strong><br>${symptoms.replace(/\n/g, "<br>")}`;
    document.getElementById('healthTip').innerHTML = `<strong>Health Tip:</strong><br>${tip}`;
}
</script>