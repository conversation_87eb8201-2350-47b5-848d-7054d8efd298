<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Shewin - Women's Health Platform</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /* General Reset and Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            font-family: 'Arial', sans-serif;
        }

        body {
            line-height: 1.6;
            background-color: #ffffff;
            color: #333;
        }

        a {
            text-decoration: none;
            color: inherit;
        }

        /* Header */
        .header {
            background-color: #e74c3c;
            padding: 1.5rem 2rem;
            text-align: center;
            color: white;
        }

        .header h1 {
            font-size: 2rem;
        }

        /* Container Section */
        .container {
            padding: 4rem 2rem;
            text-align: center;
        }

        /* Content Section */
        .content-section {
            padding: 4rem 2rem;
            background: #f9f9f9;
        }

        .content-filter {
            text-align: center;
            margin-bottom: 2rem;
        }

        .filter-btn {
            margin: 0 1rem;
            padding: 10px 25px;
            border: none;
            border-radius: 25px;
            background: #eee;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .filter-btn:hover {
            background: #ddd;
        }

        .filter-btn.active {
            background: #e74c3c;
            color: white;
        }

        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            padding: 0 2rem;
        }

        .content-card {
            background: white;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 3px 10px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }

        .content-card:hover {
            transform: translateY(-5px);
        }

        .card-image {
            height: 200px;
            background-size: cover;
            background-position: center;
        }

        .card-content {
            padding: 1.5rem;
        }

        .content-category {
            color: #e74c3c;
            text-transform: uppercase;
            font-size: 0.9rem;
            font-weight: bold;
        }

        .card-content h3 {
            margin: 0.5rem 0;
            color: #2c3e50;
        }

        .card-content p {
            color: #666;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        .view-more {
            display: inline-block;
            margin-top: 1rem;
            color: #e74c3c;
            text-decoration: none;
            font-weight: bold;
        }

        .view-more:hover {
            text-decoration: underline;
        }

        /* Services Section */
        .services {
            padding: 4rem 2rem;
            background: #fff;
            text-align: center;
        }

        /* Contact Section */
        .mh-contact-section {
            padding: 4rem 2rem;
            background: #f1f1f1;
            text-align: center;
        }

        /* Footer (Optional) */
        footer {
            background: #333;
            color: white;
            text-align: center;
            padding: 1rem 2rem;
        }
    </style>
</head>

<body>
    <!-- Existing Header -->
    <header class="header">
        <h1>Welcome to MenoW</h1>
    </header>

    <!-- Existing Container Section -->
    <section class="container">
        <h2>Empowering Women's Health</h2>
        <p>Explore articles, videos, and podcasts curated for you.</p>
    </section>

    <!-- New Content Section -->
    <section class="content-section">
        <div class="content-filter">
            <button class="filter-btn active" data-filter="all">All</button>
            <button class="filter-btn" data-filter="article">Articles</button>
            <button class="filter-btn" data-filter="video">Videos</button>
            <button class="filter-btn" data-filter="podcast">Podcasts</button>
        </div>
<!-- Inside your content-grid -->
<div class="content-grid">
    <!-- Article Content -->
    <div class="content-card" data-category="article">
        <div class="card-image" style="background-image: url('https://cdn.pixabay.com/photo/2018/06/07/16/49/virtual-3460451_1280.jpg')"></div>
        <div class="card-content">
            <span class="content-category">Article</span>
            <h3>Understanding Menstrual Hygiene</h3>
            <p>Comprehensive guide to maintaining proper menstrual hygiene and debunking common myths.</p>
            <a href="https://www.worldbank.org/en/topic/water/brief/menstrual-health-and-hygiene" class="view-more">Read Article →</a>
        </div>
    </div>

            <!-- Video Content -->
    <!-- Video Content -->
    <div class="content-card" data-category="video">
        <div class="card-image" style="background-image: url('https://cdn.pixabay.com/photo/2016/11/21/14/57/video-1846140_1280.jpg')"></div>
        <div class="card-content">
            <span class="content-category">Video</span>
            <h3>Yoga for Menstrual Comfort</h3>
            <p>10-minute yoga routine to ease menstrual cramps and improve overall well-being.</p>
            <a href="https://www.youtube.com/watch?v=4JaCcp39iVI" class="view-more">Watch Video →</a>
        </div>
    </div>


            <!-- Podcast Content -->
            <div class="content-card" data-category="podcast">
                <div class="card-image" style="background-image: url('https://cdn.pixabay.com/photo/2020/05/18/14/53/microphone-5188283_1280.jpg')"></div>
                <div class="card-content">
                    <span class="content-category">Podcast</span>
                    <h3>Women's Health Talk</h3>
                    <p>Expert discussion on breaking taboos around women's reproductive health.</p>
                    <a href="https://www.who.int/initiatives/beijing25/podcast-series-women-s-health-and-gender-inequalities" class="view-more">Listen Now →</a>
                </div>
            </div>

            <!-- Add more content cards if needed -->
        </div>
    </section>

    <!-- Existing Services Section -->
    

    <!-- Existing Contact Section -->
    <section id="2beused" class="mh-contact-section">
        <h2>Contact Us</h2>
        <p>Have questions? Reach out to our team for support.</p>
    </section>

    <!-- Authentication Modals (If any) -->
    <!-- ... -->

    <!-- New Content Filter Script -->
    <script>
        // Content Filter Functionality
        document.querySelectorAll('.filter-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                // Remove active class from all buttons
                document.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
                
                const filter = this.dataset.filter;
                const contentCards = document.querySelectorAll('.content-card');

                contentCards.forEach(card => {
                    if(filter === 'all' || card.dataset.category === filter) {
                        card.style.display = 'block';
                    } else {
                        card.style.display = 'none';
                    }
                });
            });
        });
    </script>

    <!-- Existing Botpress Script -->
    <script src="https://cdn.botpress.cloud/webchat/v2.2/webchat.min.js"></script>
    <script>
        window.botpressWebChat.init({
            // Your botpress credentials/configuration
        });
    </script>

</body>
</html>