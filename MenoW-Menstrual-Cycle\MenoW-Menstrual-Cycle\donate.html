<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Donate</title>
  <script src="https://www.paypal.com/sdk/js?client-id=YOUR_PAYPAL_CLIENT_ID"></script> <!-- Replace with your Client ID -->
  <style>
    body {
      font-family: Arial, sans-serif;
      background: linear-gradient(to right, #ff9a9e, #fad0c4);
      padding: 50px;
      text-align: center;
    }
    .donate-container {
      max-width: 600px;
      margin: 0 auto;
      background: #fff;
      padding: 30px;
      border-radius: 10px;
      box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    }
    h1 {
      color: #d63384;
      margin-bottom: 20px;
    }
    .donation-options {
      display: flex;
      justify-content: space-around;
      margin-bottom: 20px;
    }
    .donation-option input {
      display: none;
    }
    .donation-option label {
      display: block;
      padding: 10px 20px;
      border: 2px solid #d63384;
      border-radius: 8px;
      cursor: pointer;
      transition: background-color 0.3s, color 0.3s;
    }
    .donation-option input:checked + label {
      background-color: #d63384;
      color: #fff;
    }
    .custom-input {
      margin-top: 20px;
    }
    .custom-input input {
      padding: 10px;
      width: 100px;
      border: 2px solid #ddd;
      border-radius: 5px;
      text-align: center;
    }
    #paypal-button-container {
      margin-top: 30px;
    }
    .header {
        background: #fffffff7;
              color: white;
      padding: 15px 20px;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .header .logo {
      display: flex;
      align-items: center;
      font-size: 24px;
      font-weight: bold;
    }

    .header .logo img {
      height: 40px;
      margin-right: 10px;
    }

    .nav-links {
      display: flex;
      gap: 20px;
    }

    .nav-links a {
      color: rgb(0, 0, 0);
      font-weight: 500;
    }

    .nav-links a:hover {
      color: #ffcc00;
    }
  </style>
</head>
<body>
    <header class="header">
        <div class="logo">
          <img src="C:\Users\<USER>\Downloads\MenoW.jpg" alt="Logo">
        </div>
        <nav class="nav-links">
          <a href="C:\Users\<USER>\Downloads\shwin\shwin\index.html">Home</a>
            <a href="C:\Users\<USER>\Downloads\shwin\shwin\tracker.html">Trackers</a>
            <a href="C:\Users\<USER>\Downloads\shwin\shwin\DashBoard.html">Dashboard</a>
            <a href="C:\Users\<USER>\Downloads\shwin\shwin\Content.html">Content</a>

        </nav>
      </header>
  <div class="donate-container">
    <h1>Support Menstrual Health</h1>
    <p>Your contribution will help us provide education, hygiene kits, and support to those in need.</p>

    <form id="donation-form">
      <div class="donation-options">
        <div class="donation-option">
          <input type="radio" id="donation10" name="donation-amount" value="10" />
          <label for="donation10">rs10</label>
        </div>
        <div class="donation-option">
          <input type="radio" id="donation20" name="donation-amount" value="20" />
          <label for="donation20">rs20</label>
        </div>
        <div class="donation-option">
          <input type="radio" id="donation50" name="donation-amount" value="50" />
          <label for="donation50">rs50</label>
        </div>
      </div>

      <div class="custom-input">
        <label for="custom-amount">Or enter a custom amount:</label><br>
        <input type="number" id="custom-amount" name="custom-amount" placeholder="Enter amount" min="1">
      </div>

      <div id="paypal-button-container"></div>
    </form>
  </div>

  <script>
    let selectedAmount = 10; // Default amount

    // Listen for radio button changes
    document.querySelectorAll("input[name='donation-amount']").forEach((radio) => {
      radio.addEventListener("change", function () {
        selectedAmount = this.value;
        document.getElementById("custom-amount").value = ""; // Clear custom input
      });
    });

    // Listen for custom input changes
    document.getElementById("custom-amount").addEventListener("input", function () {
      selectedAmount = this.value;
      document.querySelectorAll("input[name='donation-amount']").forEach((radio) => (radio.checked = false)); // Clear radio buttons
    });

    paypal.Buttons({
      createOrder: function (data, actions) {
        return actions.order.create({
          purchase_units: [
            {
              amount: {
                value: selectedAmount || "10", // Use default if nothing is selected
              },
            },
          ],
        });
      },
      onApprove: function (data, actions) {
        return actions.order.capture().then(function (details) {
          alert("Thank you for your generous donation, " + details.payer.name.given_name + "!");
        });
      },
    }).render("#paypal-button-container");
  </script>
  <footer class="footer">
    <div class="footer-links">
        <a href="C:\Users\<USER>\Downloads\shwin\shwin\about.html">About</a>
        <a href="C:\Users\<USER>\Downloads\shwin\shwin\contact.html">Contact</a>
    </div>
    <p class="footer-copy">&copy; 2025 Shewin. All rights reserved.</p>
</footer>
</body>
</html>
